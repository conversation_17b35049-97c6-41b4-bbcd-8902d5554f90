import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiExtraModels, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { MediaUserService } from '../services/media-user.service';
import { MediaCreateResponseDto, MediaDto } from '../../dto/media-user.dto';
import { MediaQueryDto } from '@/modules/data/media/dto/media-query.dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { MEDIA_ERROR_CODES } from '../../exception';
import { ErrorCode } from '@/common';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { DeleteMediaDto } from '../../dto/delete-media.dto';
import { MediaUploadUrlDto } from '../../dto/media-upload-url.dto';
import { MediaUploadDto } from '../../dto/media.dto';

/**
 * Controller handling APIs related to user media management
 */
@ApiTags(SWAGGER_API_TAGS.USER_MEDIA)
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  MediaDto,
  MediaQueryDto,
  DeleteMediaDto,
  ApiErrorResponseDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('media')
export class MediaUserController {
  constructor(private readonly mediaUserService: MediaUserService) {}

  /**
   * Get list of media for the current user
   */
  @Get('my-media')
  @ApiOperation({
    summary: 'Lấy danh sách media của người dùng hiện tại',
    description: 'Lấy danh sách media thuộc sở hữu của người dùng hiện tại với khả năng tìm kiếm, lọc và phân trang'
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Từ khóa tìm kiếm trong tên hoặc mô tả media',
    example: 'beautiful image'
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: ['DRAFT', 'APPROVED', 'PENDING', 'REJECTED'],
    description: 'Trạng thái của media',
    example: 'APPROVED'
  })
  @ApiQuery({
    name: 'ownerType',
    required: false,
    enum: ['USER', 'ADMIN'],
    description: 'Loại chủ sở hữu media',
    example: 'USER'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang (bắt đầu từ 1)',
    example: 1,
    minimum: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Số lượng kết quả trên một trang',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Trường sắp xếp',
    example: 'createdAt'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Hướng sắp xếp',
    example: 'DESC'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách media thành công.',
    schema: ApiResponseDto.getPaginatedSchema(MediaDto),
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    MEDIA_ERROR_CODES.DATA_FETCH_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @HttpCode(HttpStatus.OK)
  async findMyMedia(
    @CurrentUser() user: JwtPayload,
    @Query() query: MediaQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<MediaDto>>> {
    const result = await this.mediaUserService.findAllByUser(user.sub, query);
    return ApiResponseDto.paginated(result, 'Lấy danh sách media thành công.');
  }

  /**
   * Get media details by ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết media theo ID',
    description: 'Lấy thông tin chi tiết của một media cụ thể theo ID'
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    description: 'ID của media (UUID)',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin chi tiết media thành công.',
    schema: ApiResponseDto.getSchema(MediaDto),
  })
  @ApiErrorResponse(
    MEDIA_ERROR_CODES.NOT_FOUND,
    MEDIA_ERROR_CODES.FORBIDDEN,
    MEDIA_ERROR_CODES.DATA_FETCH_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @HttpCode(HttpStatus.OK)
  async findOne(
    @CurrentUser() user: JwtPayload,
    @Param('id') id: string,
  ): Promise<ApiResponseDto<MediaDto>> {
    const result = await this.mediaUserService.findById(id, user.sub);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết media thành công.');
  }

  /**
   * Xóa mềm nhiều media cho người dùng hiện tại (cập nhật trạng thái thành DELETED)
   */
  @Delete('my-media')
  @ApiOperation({
    summary: 'Xóa mềm nhiều media cho người dùng hiện tại',
    description: 'Xóa mềm nhiều media cùng lúc bằng cách cập nhật trạng thái thành DELETED. Media sẽ không bị xóa vĩnh viễn khỏi hệ thống.'
  })
  @ApiBody({
    type: DeleteMediaDto,
    description: 'Danh sách ID của các media cần xóa mềm',
    examples: {
      'single': {
        summary: 'Xóa một media',
        description: 'Xóa mềm một media duy nhất',
        value: {
          mediaIds: ['123e4567-e89b-12d3-a456-************']
        }
      },
      'multiple': {
        summary: 'Xóa nhiều media',
        description: 'Xóa mềm nhiều media cùng lúc',
        value: {
          mediaIds: [
            '123e4567-e89b-12d3-a456-************',
            '123e4567-e89b-12d3-a456-426614174001',
            '123e4567-e89b-12d3-a456-426614174002'
          ]
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Media đã được xóa mềm thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Media đã được xóa mềm thành công.' },
        result: { type: 'null', example: null }
      }
    }
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    MEDIA_ERROR_CODES.NOT_FOUND,
    MEDIA_ERROR_CODES.FORBIDDEN,
    MEDIA_ERROR_CODES.BAD_REQUEST,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @HttpCode(HttpStatus.OK)
  async deleteManyMyMedia(
    @CurrentUser() user: JwtPayload,
    @Body() dto: DeleteMediaDto,
  ): Promise<ApiResponseDto<null>> {
    await this.mediaUserService.deleteManyByUser(user.id, dto.mediaIds);
    return ApiResponseDto.success(null, 'Media đã được xóa mềm thành công.');
  }

  /**
   * Create presigned URLs for media
   */
  @Post('presigned-urls')
  @ApiOperation({
    summary: 'Tạo presigned URLs cho danh sách media',
    description: 'Tạo presigned URLs để truy cập các media từ danh sách media được cung cấp. URLs này có thời hạn và cho phép truy cập trực tiếp đến media.'
  })
  @ApiBody({
    type: MediaDto,
    isArray: true,
    description: 'Danh sách media cần tạo presigned URLs',
    examples: {
      'basic': {
        summary: 'Tạo presigned URLs cơ bản',
        description: 'Tạo presigned URLs cho danh sách media',
        value: [
          {
            name: 'My beautiful image',
            description: 'An image uploaded by user',
            size: 1048576,
            tags: ['image', 'beautiful'],
            type: 'image/png',
            viewUrl: 'test/test-1744857340088-abc123'
          },
          {
            name: 'Demo video',
            description: 'A demo video file',
            size: 5242880,
            tags: ['video', 'demo'],
            type: 'video/mp4',
            viewUrl: 'test/test-1744857340089-def456'
          }
        ]
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo presigned URLs thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Tạo presigned URLs thành công.' },
        result: {
          type: 'array',
          items: { type: 'string' },
          example: [
            'https://s3.amazonaws.com/bucket/test/test-1744857340088-abc123?X-Amz-Algorithm=...',
            'https://s3.amazonaws.com/bucket/test/test-1744857340089-def456?X-Amz-Algorithm=...'
          ]
        }
      }
    }
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    MEDIA_ERROR_CODES.BAD_REQUEST,
    MEDIA_ERROR_CODES.GENERAL_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @HttpCode(HttpStatus.CREATED)
  async createPresignedUrlsFromMediaList(
    @CurrentUser() user: JwtPayload,
    @Body() mediaList: MediaDto[],
  ): Promise<ApiResponseDto<string[]>> {
    const result = await this.mediaUserService.createPresignedUrlsFromMediaList(
      mediaList,
      user.sub,
    );
    return ApiResponseDto.created(result, 'Tạo presigned URLs thành công.');
  }

  @Post('upload-image')
  @ApiOperation({
    summary: 'Tạo URL upload media',
    description: 'Tạo URL upload cho nhiều media files (hình ảnh, video, audio) cho người dùng'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo URL upload media thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 0 },
        message: { type: 'string', example: 'Tạo URL upload media thành công.' },
        result: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', example: 'media-1744857340088-0' },
              name: { type: 'string', example: 'my-image.jpg' },
              description: { type: 'string', example: 'An image uploaded by user', nullable: true },
              size: { type: 'number', example: 1048576 },
              tags: {
                type: 'array',
                items: { type: 'string' },
                example: ['gif', 'funny']
              },
              type: { type: 'string', example: 'image/jpeg' },
              viewUrl: { type: 'string', example: 'https://cdn.example.com/media/images/2023/04/12/my-image-1681289012345.jpg' }
            }
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    ErrorCode.VALIDATION_ERROR,
    MEDIA_ERROR_CODES.BAD_REQUEST,
    MEDIA_ERROR_CODES.GENERAL_ERROR,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  @HttpCode(HttpStatus.OK)
  async createMediaUploadUrl(
    @CurrentUser('id') userId: number,
    @Body() dto: MediaUploadUrlDto[],
  ): Promise<ApiResponseDto<MediaCreateResponseDto[]>> {
    const result = await this.mediaUserService.createImageUploadUrl(
      userId,
      dto,
    );
    return ApiResponseDto.created(result, 'Tạo URL upload ảnh thành công.');
  }
}
