# User Integration API Examples - Admin Module

## Tổng quan

Module Integration Admin đã được mở rộng với các API mới cho phép admin quản lý và xem danh sách tích hợp của người dùng. Các API này bổ sung cho các API hiện tại và cung cấp khả năng giám sát tích hợp của tất cả người dùng trong hệ thống.

## Các API mới được thêm

### 1. <PERSON><PERSON><PERSON> danh sách tích hợp của tất cả người dùng
### 2. <PERSON><PERSON><PERSON> danh sách tích hợp của người dùng cụ thể

---

## API 1: L<PERSON>y danh sách tích hợp của tất cả người dùng

### Endpoint
```
GET /admin/integration/users
```

### Authentication
- **Required**: JWT Employee Token
- **Guard**: JwtEmployeeGuard

### Headers
```
Authorization: Bearer <JWT_EMPLOYEE_TOKEN>
Content-Type: application/json
```

### Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| page | number | No | Số trang (mặc định: 1) | 1 |
| limit | number | No | Số lượng item mỗi trang (mặc định: 10) | 20 |
| search | string | No | Tìm kiếm theo tên tích hợp | "Google" |
| type | string | No | Lọc theo loại tích hợp | "ANALYTICS" |
| ownedType | enum | No | Lọc theo loại chủ sở hữu | "USER" hoặc "ADMIN" |
| userId | number | No | Lọc theo ID người dùng cụ thể | 123 |
| sortBy | string | No | Sắp xếp theo trường (mặc định: created_at) | "integration_name" |
| sortDirection | string | No | Hướng sắp xếp (mặc định: DESC) | "ASC" |

### Request Examples

#### Ví dụ 1: Lấy tất cả tích hợp của người dùng
```bash
GET /admin/integration/users?page=1&limit=10
```

#### Ví dụ 2: Tìm kiếm tích hợp theo tên
```bash
GET /admin/integration/users?search=Google&page=1&limit=10
```

#### Ví dụ 3: Lọc theo loại tích hợp
```bash
GET /admin/integration/users?type=ANALYTICS&ownedType=USER
```

#### Ví dụ 4: Lọc theo người dùng cụ thể
```bash
GET /admin/integration/users?userId=123&page=1&limit=5
```

### Response Success (200)

```json
{
  "code": 200,
  "message": "Lấy danh sách tích hợp người dùng thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "integrationName": "Google Analytics",
        "type": "ANALYTICS",
        "userId": 123,
        "info": {
          "apiKey": "***",
          "endpoint": "https://api.google.com"
        },
        "ownedType": "USER",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "employeeId": null
      },
      {
        "id": 2,
        "integrationName": "Facebook Ads",
        "type": "ADVERTISING",
        "userId": 456,
        "info": {
          "accessToken": "***",
          "accountId": "*********"
        },
        "ownedType": "USER",
        "createdAt": "2024-01-02T00:00:00.000Z",
        "employeeId": null
      }
    ],
    "meta": {
      "totalItems": 50,
      "itemCount": 2,
      "itemsPerPage": 10,
      "totalPages": 5,
      "currentPage": 1
    }
  }
}
```

### Response Error (400)

```json
{
  "code": 400,
  "message": "Validation failed",
  "result": null
}
```

---

## API 2: Lấy danh sách tích hợp của người dùng cụ thể

### Endpoint
```
GET /admin/integration/users/:userId
```

### Authentication
- **Required**: JWT Employee Token
- **Guard**: JwtEmployeeGuard

### Headers
```
Authorization: Bearer <JWT_EMPLOYEE_TOKEN>
Content-Type: application/json
```

### Path Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| userId | number | Yes | ID của người dùng | 123 |

### Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| page | number | No | Số trang (mặc định: 1) | 1 |
| limit | number | No | Số lượng item mỗi trang (mặc định: 10) | 20 |
| search | string | No | Tìm kiếm theo tên tích hợp | "Google" |
| type | string | No | Lọc theo loại tích hợp | "ANALYTICS" |
| ownedType | enum | No | Lọc theo loại chủ sở hữu | "USER" hoặc "ADMIN" |
| sortBy | string | No | Sắp xếp theo trường (mặc định: created_at) | "integration_name" |
| sortDirection | string | No | Hướng sắp xếp (mặc định: DESC) | "ASC" |

### Request Examples

#### Ví dụ 1: Lấy tất cả tích hợp của người dùng ID 123
```bash
GET /admin/integration/users/123?page=1&limit=10
```

#### Ví dụ 2: Tìm kiếm tích hợp Google của người dùng ID 123
```bash
GET /admin/integration/users/123?search=Google&page=1&limit=10
```

#### Ví dụ 3: Lọc theo loại tích hợp của người dùng ID 123
```bash
GET /admin/integration/users/123?type=ANALYTICS&ownedType=USER
```

### Response Success (200)

```json
{
  "code": 200,
  "message": "Lấy danh sách tích hợp của người dùng thành công",
  "result": {
    "items": [
      {
        "id": 1,
        "integrationName": "Google Analytics",
        "type": "ANALYTICS",
        "userId": 123,
        "info": {
          "apiKey": "***",
          "endpoint": "https://api.google.com"
        },
        "ownedType": "USER",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "employeeId": null
      },
      {
        "id": 3,
        "integrationName": "Mailchimp",
        "type": "EMAIL_MARKETING",
        "userId": 123,
        "info": {
          "apiKey": "***",
          "listId": "abc123"
        },
        "ownedType": "USER",
        "createdAt": "2024-01-03T00:00:00.000Z",
        "employeeId": null
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 2,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### Response Error (400)

```json
{
  "code": 400,
  "message": "Validation failed",
  "result": null
}
```

---

## Sự khác biệt với API hiện tại

### API hiện tại (GET /admin/integration)
- **Mục đích**: Lấy danh sách tích hợp do chính admin đó tạo
- **Filter**: Tự động filter theo adminId
- **Use case**: Admin quản lý tích hợp của chính mình

### API mới (GET /admin/integration/users)
- **Mục đích**: Lấy danh sách tích hợp của tất cả người dùng
- **Filter**: Không filter theo adminId, có thể filter theo userId
- **Use case**: Admin giám sát tích hợp của tất cả người dùng trong hệ thống

### API mới (GET /admin/integration/users/:userId)
- **Mục đích**: Lấy danh sách tích hợp của người dùng cụ thể
- **Filter**: Filter theo userId được chỉ định
- **Use case**: Admin xem chi tiết tích hợp của một người dùng cụ thể

---

## Lưu ý quan trọng

1. **Bảo mật**: Chỉ employee có quyền admin mới có thể truy cập các API này
2. **Phân trang**: Tất cả API đều hỗ trợ phân trang để tối ưu hiệu suất
3. **Tìm kiếm**: Hỗ trợ tìm kiếm theo tên tích hợp (case-insensitive)
4. **Lọc**: Hỗ trợ lọc theo nhiều tiêu chí khác nhau
5. **Sắp xếp**: Hỗ trợ sắp xếp theo các trường khác nhau

## Cách sử dụng

1. **Đăng nhập**: Lấy JWT token của employee
2. **Gọi API**: Sử dụng token trong header Authorization
3. **Xử lý response**: Parse JSON response và hiển thị dữ liệu
4. **Phân trang**: Sử dụng meta information để implement phân trang

## Testing

Các API đã được test và sẵn sàng sử dụng:

```bash
# Build project
npm run build

# Start server
npm run start:dev

# Test API với Postman hoặc curl
curl -H "Authorization: Bearer <JWT_TOKEN>" \
     -H "Content-Type: application/json" \
     "http://localhost:3000/admin/integration/users?page=1&limit=10"
```
