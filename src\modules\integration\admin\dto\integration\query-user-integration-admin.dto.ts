import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto';
import { OwnedTypeEnum } from '@modules/integration/enums';

/**
 * DTO cho việc truy vấn danh sách tích hợp của người dùng bởi admin
 */
export class QueryUserIntegrationAdminDto extends QueryDto {
  /**
   * Tìm kiếm theo tên tích hợp
   * @example "Google"
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên tích hợp',
    example: 'Google'
  })
  @IsOptional()
  @IsString()
  declare search?: string;

  /**
   * Lọc theo loại tích hợp
   * @example "ANALYTICS"
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại tích hợp',
    example: 'ANALYTICS'
  })
  @IsOptional()
  @IsString()
  type?: string;

  /**
   * Lọc theo loại chủ sở hữu tích hợp
   * @example "USER"
   */
  @ApiPropertyOptional({
    description: 'Lọc theo loại chủ sở hữu tích hợp',
    enum: OwnedTypeEnum,
    example: OwnedTypeEnum.USER
  })
  @IsOptional()
  @IsEnum(OwnedTypeEnum)
  ownedType?: OwnedTypeEnum;

  /**
   * Lọc theo ID người dùng cụ thể
   * @example 123
   */
  @ApiPropertyOptional({
    description: 'Lọc theo ID người dùng cụ thể',
    example: 123
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;
}
